// Security utilities and middleware for Esus Audit AI
const rateLimit = require('express-rate-limit');
const helmet = require('helmet');

class SecurityService {
    constructor() {
        this.maxLoginAttempts = 5;
        this.lockoutDuration = 15 * 60 * 1000; // 15 minutes
        this.loginAttempts = new Map();
    }

    // Rate limiting configuration
    createRateLimit(windowMs = 15 * 60 * 1000, max = 100) {
        return rateLimit({
            windowMs: windowMs,
            max: max,
            message: {
                error: 'Too many requests from this IP, please try again later.'
            },
            standardHeaders: true,
            legacyHeaders: false,
        });
    }

    // Helmet security headers configuration
    getHelmetConfig() {
        return helmet({
            contentSecurityPolicy: {
                directives: {
                    defaultSrc: ["'self'"],
                    styleSrc: ["'self'", "'unsafe-inline'", "https://fonts.googleapis.com"],
                    fontSrc: ["'self'", "https://fonts.gstatic.com"],
                    imgSrc: ["'self'", "data:", "https:"],
                    scriptSrc: ["'self'"],
                    connectSrc: ["'self'", "https://*.azure.com", "https://*.azurewebsites.net"],
                },
            },
            hsts: {
                maxAge: 31536000,
                includeSubDomains: true,
                preload: true
            }
        });
    }

    // Input validation and sanitization
    validateInput(input, type) {
        if (!input) return false;

        switch (type) {
            case 'email':
                const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
                return emailRegex.test(input) && input.length <= 255;
            
            case 'password':
                // At least 8 characters, 1 uppercase, 1 lowercase, 1 number, 1 special char
                const passwordRegex = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}$/;
                return passwordRegex.test(input) && input.length <= 128;
            
            case 'name':
                const nameRegex = /^[a-zA-Z\s'-]{1,100}$/;
                return nameRegex.test(input);
            
            case 'uuid':
                const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
                return uuidRegex.test(input);
            
            case 'filename':
                const filenameRegex = /^[a-zA-Z0-9._-]+$/;
                return filenameRegex.test(input) && input.length <= 255;
            
            default:
                return false;
        }
    }

    // Sanitize string input
    sanitizeString(input) {
        if (typeof input !== 'string') return '';
        
        return input
            .trim()
            .replace(/[<>]/g, '') // Remove potential HTML tags
            .substring(0, 1000); // Limit length
    }

    // Check for login attempts and implement lockout
    checkLoginAttempts(identifier) {
        const attempts = this.loginAttempts.get(identifier);
        
        if (!attempts) {
            return { allowed: true, remaining: this.maxLoginAttempts };
        }

        const now = Date.now();
        
        // Clean up old attempts
        const recentAttempts = attempts.filter(
            timestamp => now - timestamp < this.lockoutDuration
        );

        if (recentAttempts.length >= this.maxLoginAttempts) {
            const oldestAttempt = Math.min(...recentAttempts);
            const timeUntilUnlock = this.lockoutDuration - (now - oldestAttempt);
            
            return {
                allowed: false,
                remaining: 0,
                lockoutTimeRemaining: timeUntilUnlock
            };
        }

        return {
            allowed: true,
            remaining: this.maxLoginAttempts - recentAttempts.length
        };
    }

    // Record a failed login attempt
    recordFailedLogin(identifier) {
        const attempts = this.loginAttempts.get(identifier) || [];
        attempts.push(Date.now());
        this.loginAttempts.set(identifier, attempts);
    }

    // Clear login attempts on successful login
    clearLoginAttempts(identifier) {
        this.loginAttempts.delete(identifier);
    }

    // Validate file upload security
    validateFileUpload(file) {
        const allowedTypes = [
            'application/pdf',
            'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            'application/vnd.ms-excel',
            'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
            'application/msword',
            'text/csv'
        ];

        const maxSize = 50 * 1024 * 1024; // 50MB

        if (!allowedTypes.includes(file.mimetype)) {
            return {
                valid: false,
                error: 'File type not allowed'
            };
        }

        if (file.size > maxSize) {
            return {
                valid: false,
                error: 'File size exceeds maximum limit'
            };
        }

        // Check for suspicious file names
        const suspiciousPatterns = [
            /\.exe$/i,
            /\.bat$/i,
            /\.cmd$/i,
            /\.scr$/i,
            /\.js$/i,
            /\.php$/i,
            /\.asp$/i,
            /\.jsp$/i
        ];

        if (suspiciousPatterns.some(pattern => pattern.test(file.originalname))) {
            return {
                valid: false,
                error: 'Suspicious file extension detected'
            };
        }

        return { valid: true };
    }

    // Generate secure random tokens
    generateSecureToken(length = 32) {
        const crypto = require('crypto');
        return crypto.randomBytes(length).toString('hex');
    }

    // Hash sensitive data
    hashData(data) {
        const crypto = require('crypto');
        return crypto.createHash('sha256').update(data).digest('hex');
    }

    // Audit log entry
    createAuditLog(userId, action, resource, details = {}) {
        return {
            timestamp: new Date().toISOString(),
            userId: userId,
            action: action,
            resource: resource,
            details: details,
            ip: details.ip || 'unknown',
            userAgent: details.userAgent || 'unknown'
        };
    }
}

module.exports = SecurityService;
