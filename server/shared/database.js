const { Pool } = require('pg');

class Database {
    constructor() {
        this.pool = new Pool({
            connectionString: process.env.DATABASE_URL,
            ssl: process.env.NODE_ENV === 'production' ? { rejectUnauthorized: false } : false
        });
    }

    async query(text, params) {
        const client = await this.pool.connect();
        try {
            const result = await client.query(text, params);
            return result;
        } finally {
            client.release();
        }
    }

    // User operations
    async createUser(userData) {
        const { email, passwordHash, firstName, lastName, role, company } = userData;
        const query = `
            INSERT INTO users (email, password_hash, first_name, last_name, role, company)
            VALUES ($1, $2, $3, $4, $5, $6)
            RETURNING id, email, first_name, last_name, role, company, created_at
        `;
        const result = await this.query(query, [email, passwordHash, firstName, lastName, role, company]);
        return result.rows[0];
    }

    async getUserByEmail(email) {
        const query = 'SELECT * FROM users WHERE email = $1 AND is_active = true';
        const result = await this.query(query, [email]);
        return result.rows[0];
    }

    async getUserById(id) {
        const query = 'SELECT id, email, first_name, last_name, role, company, created_at FROM users WHERE id = $1 AND is_active = true';
        const result = await this.query(query, [id]);
        return result.rows[0];
    }

    // Project operations
    async createProject(projectData) {
        const { name, description, clientName, clientEmail, createdBy, assignedTo, startDate, endDate } = projectData;
        const query = `
            INSERT INTO projects (name, description, client_name, client_email, created_by, assigned_to, start_date, end_date)
            VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
            RETURNING *
        `;
        const result = await this.query(query, [name, description, clientName, clientEmail, createdBy, assignedTo, startDate, endDate]);
        return result.rows[0];
    }

    async getProjectsByUser(userId) {
        const query = `
            SELECT * FROM projects 
            WHERE created_by = $1 OR $1 = ANY(assigned_to)
            ORDER BY created_at DESC
        `;
        const result = await this.query(query, [userId]);
        return result.rows;
    }

    async getProjectById(id) {
        const query = 'SELECT * FROM projects WHERE id = $1';
        const result = await this.query(query, [id]);
        return result.rows[0];
    }

    async updateProject(id, updates) {
        const setClause = Object.keys(updates).map((key, index) => `${key} = $${index + 2}`).join(', ');
        const query = `UPDATE projects SET ${setClause} WHERE id = $1 RETURNING *`;
        const values = [id, ...Object.values(updates)];
        const result = await this.query(query, values);
        return result.rows[0];
    }

    // Document operations
    async createDocument(documentData) {
        const { projectId, uploadedBy, originalName, filePath, fileSize, fileType, blobUrl } = documentData;
        const query = `
            INSERT INTO documents (project_id, uploaded_by, original_name, file_path, file_size, file_type, blob_url)
            VALUES ($1, $2, $3, $4, $5, $6, $7)
            RETURNING *
        `;
        const result = await this.query(query, [projectId, uploadedBy, originalName, filePath, fileSize, fileType, blobUrl]);
        return result.rows[0];
    }

    async getDocumentsByProject(projectId) {
        const query = 'SELECT * FROM documents WHERE project_id = $1 ORDER BY created_at DESC';
        const result = await this.query(query, [projectId]);
        return result.rows;
    }

    async updateDocumentStatus(id, status) {
        const query = 'UPDATE documents SET status = $1 WHERE id = $2 RETURNING *';
        const result = await this.query(query, [status, id]);
        return result.rows[0];
    }

    // Analysis results operations
    async createAnalysisResult(analysisData) {
        const { documentId, extractedData, aiSummary, redFlags, highlights, confidenceScore, processingTime } = analysisData;
        const query = `
            INSERT INTO analysis_results (document_id, extracted_data, ai_summary, red_flags, highlights, confidence_score, processing_time_ms)
            VALUES ($1, $2, $3, $4, $5, $6, $7)
            RETURNING *
        `;
        const result = await this.query(query, [documentId, extractedData, aiSummary, redFlags, highlights, confidenceScore, processingTime]);
        return result.rows[0];
    }

    async getAnalysisResultsByDocument(documentId) {
        const query = 'SELECT * FROM analysis_results WHERE document_id = $1';
        const result = await this.query(query, [documentId]);
        return result.rows[0];
    }

    async getAnalysisResultsByProject(projectId) {
        const query = `
            SELECT ar.*, d.original_name, d.file_type 
            FROM analysis_results ar
            JOIN documents d ON ar.document_id = d.id
            WHERE d.project_id = $1
            ORDER BY ar.created_at DESC
        `;
        const result = await this.query(query, [projectId]);
        return result.rows;
    }

    // Chat history operations
    async createChatEntry(chatData) {
        const { projectId, userId, question, answer, contextDocuments } = chatData;
        const query = `
            INSERT INTO chat_history (project_id, user_id, question, answer, context_documents)
            VALUES ($1, $2, $3, $4, $5)
            RETURNING *
        `;
        const result = await this.query(query, [projectId, userId, question, answer, contextDocuments]);
        return result.rows[0];
    }

    async getChatHistory(projectId, limit = 50) {
        const query = `
            SELECT ch.*, u.first_name, u.last_name 
            FROM chat_history ch
            JOIN users u ON ch.user_id = u.id
            WHERE ch.project_id = $1
            ORDER BY ch.created_at DESC
            LIMIT $2
        `;
        const result = await this.query(query, [projectId, limit]);
        return result.rows;
    }

    // Audit reports operations
    async createAuditReport(reportData) {
        const { projectId, generatedBy, reportName, reportData, pdfUrl } = reportData;
        const query = `
            INSERT INTO audit_reports (project_id, generated_by, report_name, report_data, pdf_url)
            VALUES ($1, $2, $3, $4, $5)
            RETURNING *
        `;
        const result = await this.query(query, [projectId, generatedBy, reportName, reportData, pdfUrl]);
        return result.rows[0];
    }

    async getAuditReportsByProject(projectId) {
        const query = 'SELECT * FROM audit_reports WHERE project_id = $1 ORDER BY created_at DESC';
        const result = await this.query(query, [projectId]);
        return result.rows;
    }

    async close() {
        await this.pool.end();
    }
}

module.exports = Database;
