const jwt = require('jsonwebtoken');
const bcrypt = require('bcryptjs');

class AuthService {
    constructor() {
        this.jwtSecret = process.env.JWT_SECRET;
        this.jwtExpiresIn = process.env.JWT_EXPIRES_IN || '24h';
    }

    async hashPassword(password) {
        const saltRounds = 10;
        return await bcrypt.hash(password, saltRounds);
    }

    async comparePassword(password, hashedPassword) {
        return await bcrypt.compare(password, hashedPassword);
    }

    generateToken(user) {
        const payload = {
            id: user.id,
            email: user.email,
            role: user.role,
            firstName: user.first_name,
            lastName: user.last_name
        };

        return jwt.sign(payload, this.jwtSecret, { expiresIn: this.jwtExpiresIn });
    }

    verifyToken(token) {
        try {
            return jwt.verify(token, this.jwtSecret);
        } catch (error) {
            throw new Error('Invalid or expired token');
        }
    }

    extractTokenFromHeader(authHeader) {
        if (!authHeader || !authHeader.startsWith('Bearer ')) {
            throw new Error('No valid authorization header found');
        }
        return authHeader.substring(7);
    }

    // Middleware function for Azure Functions
    authenticateRequest(req) {
        try {
            const authHeader = req.headers.authorization;
            const token = this.extractTokenFromHeader(authHeader);
            const decoded = this.verifyToken(token);
            return decoded;
        } catch (error) {
            throw new Error('Authentication failed: ' + error.message);
        }
    }

    // Role-based authorization
    authorizeRole(user, allowedRoles) {
        if (!allowedRoles.includes(user.role)) {
            throw new Error('Insufficient permissions');
        }
        return true;
    }

    // Check if user can access project
    canAccessProject(user, project) {
        // Admin can access all projects
        if (user.role === 'admin') {
            return true;
        }

        // Project creator can access
        if (project.created_by === user.id) {
            return true;
        }

        // Assigned users can access
        if (project.assigned_to && project.assigned_to.includes(user.id)) {
            return true;
        }

        return false;
    }

    validateEmail(email) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(email);
    }

    validatePassword(password) {
        // At least 8 characters, 1 uppercase, 1 lowercase, 1 number
        const passwordRegex = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)[a-zA-Z\d@$!%*?&]{8,}$/;
        return passwordRegex.test(password);
    }

    sanitizeUser(user) {
        // Remove sensitive information before sending to client
        const { password_hash, ...sanitizedUser } = user;
        return sanitizedUser;
    }
}

module.exports = AuthService;
