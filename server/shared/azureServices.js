const { BlobServiceClient } = require('@azure/storage-blob');
const { DocumentAnalysisClient, AzureKeyCredential } = require('@azure/ai-form-recognizer');
const { SearchClient, SearchIndexClient, AzureKeyCredential: SearchKeyCredential } = require('@azure/search-documents');

class AzureServices {
    constructor() {
        // Initialize Blob Storage
        this.blobServiceClient = BlobServiceClient.fromConnectionString(
            process.env.AZURE_STORAGE_CONNECTION_STRING
        );
        this.containerName = process.env.AZURE_STORAGE_CONTAINER_NAME;

        // Initialize Form Recognizer
        this.formRecognizerClient = new DocumentAnalysisClient(
            process.env.AZURE_FORM_RECOGNIZER_ENDPOINT,
            new AzureKeyCredential(process.env.AZURE_FORM_RECOGNIZER_KEY)
        );

        // Initialize Cognitive Search
        this.searchClient = new SearchClient(
            process.env.AZURE_SEARCH_ENDPOINT,
            process.env.AZURE_SEARCH_INDEX_NAME,
            new SearchKeyCredential(process.env.AZURE_SEARCH_API_KEY)
        );
    }

    async uploadToBlob(fileName, fileBuffer, contentType) {
        try {
            const containerClient = this.blobServiceClient.getContainerClient(this.containerName);
            
            // Ensure container exists
            await containerClient.createIfNotExists({
                access: 'blob'
            });

            const blockBlobClient = containerClient.getBlockBlobClient(fileName);
            
            const uploadOptions = {
                blobHTTPHeaders: {
                    blobContentType: contentType
                }
            };

            await blockBlobClient.upload(fileBuffer, fileBuffer.length, uploadOptions);
            
            return {
                url: blockBlobClient.url,
                fileName: fileName,
                size: fileBuffer.length
            };
        } catch (error) {
            console.error('Blob upload error:', error);
            throw new Error('Failed to upload file to Azure Blob Storage');
        }
    }

    async analyzeDocument(blobUrl, documentType = 'prebuilt-document') {
        try {
            const poller = await this.formRecognizerClient.beginAnalyzeDocumentFromUrl(
                documentType,
                blobUrl
            );

            const result = await poller.pollUntilDone();
            
            return this.extractDocumentData(result, documentType);
        } catch (error) {
            console.error('Form Recognizer error:', error);
            throw new Error('Failed to analyze document with Form Recognizer');
        }
    }

    extractDocumentData(result, documentType) {
        const extractedData = {
            content: result.content,
            pages: result.pages?.length || 0,
            tables: [],
            keyValuePairs: {},
            entities: []
        };

        // Extract tables
        if (result.tables) {
            extractedData.tables = result.tables.map(table => ({
                rowCount: table.rowCount,
                columnCount: table.columnCount,
                cells: table.cells.map(cell => ({
                    content: cell.content,
                    rowIndex: cell.rowIndex,
                    columnIndex: cell.columnIndex
                }))
            }));
        }

        // Extract key-value pairs
        if (result.keyValuePairs) {
            result.keyValuePairs.forEach(kvp => {
                if (kvp.key && kvp.value) {
                    extractedData.keyValuePairs[kvp.key.content] = kvp.value.content;
                }
            });
        }

        // Extract entities (for financial documents)
        if (result.entities) {
            extractedData.entities = result.entities.map(entity => ({
                category: entity.category,
                content: entity.content,
                confidence: entity.confidence
            }));
        }

        // Extract specific financial data based on document type
        if (documentType === 'prebuilt-invoice') {
            extractedData.invoice = this.extractInvoiceData(result);
        } else if (documentType === 'prebuilt-receipt') {
            extractedData.receipt = this.extractReceiptData(result);
        }

        return extractedData;
    }

    extractInvoiceData(result) {
        const invoice = {};
        
        if (result.documents && result.documents[0]) {
            const doc = result.documents[0];
            const fields = doc.fields;

            if (fields) {
                invoice.invoiceId = fields.InvoiceId?.value;
                invoice.invoiceDate = fields.InvoiceDate?.value;
                invoice.dueDate = fields.DueDate?.value;
                invoice.vendorName = fields.VendorName?.value;
                invoice.vendorAddress = fields.VendorAddress?.value;
                invoice.customerName = fields.CustomerName?.value;
                invoice.customerAddress = fields.CustomerAddress?.value;
                invoice.subtotal = fields.SubTotal?.value;
                invoice.totalTax = fields.TotalTax?.value;
                invoice.invoiceTotal = fields.InvoiceTotal?.value;
                
                // Extract line items
                if (fields.Items?.value) {
                    invoice.items = fields.Items.value.map(item => ({
                        description: item.value?.Description?.value,
                        quantity: item.value?.Quantity?.value,
                        unitPrice: item.value?.UnitPrice?.value,
                        amount: item.value?.Amount?.value
                    }));
                }
            }
        }

        return invoice;
    }

    extractReceiptData(result) {
        const receipt = {};
        
        if (result.documents && result.documents[0]) {
            const doc = result.documents[0];
            const fields = doc.fields;

            if (fields) {
                receipt.merchantName = fields.MerchantName?.value;
                receipt.merchantAddress = fields.MerchantAddress?.value;
                receipt.transactionDate = fields.TransactionDate?.value;
                receipt.transactionTime = fields.TransactionTime?.value;
                receipt.total = fields.Total?.value;
                receipt.subtotal = fields.Subtotal?.value;
                receipt.tax = fields.TotalTax?.value;
                
                // Extract items
                if (fields.Items?.value) {
                    receipt.items = fields.Items.value.map(item => ({
                        name: item.value?.Name?.value,
                        quantity: item.value?.Quantity?.value,
                        price: item.value?.Price?.value,
                        totalPrice: item.value?.TotalPrice?.value
                    }));
                }
            }
        }

        return receipt;
    }

    async indexDocument(documentId, content, metadata) {
        try {
            const document = {
                id: documentId,
                content: content,
                metadata: JSON.stringify(metadata),
                timestamp: new Date().toISOString()
            };

            await this.searchClient.uploadDocuments([document]);
            return true;
        } catch (error) {
            console.error('Search indexing error:', error);
            throw new Error('Failed to index document for search');
        }
    }

    async searchDocuments(query, projectId = null, top = 10) {
        try {
            let searchOptions = {
                top: top,
                includeTotalCount: true
            };

            if (projectId) {
                searchOptions.filter = `metadata/any(m: m eq 'project_id:${projectId}')`;
            }

            const searchResults = await this.searchClient.search(query, searchOptions);
            
            const results = [];
            for await (const result of searchResults.results) {
                results.push({
                    id: result.document.id,
                    content: result.document.content,
                    metadata: JSON.parse(result.document.metadata || '{}'),
                    score: result.score
                });
            }

            return {
                results: results,
                count: searchResults.count
            };
        } catch (error) {
            console.error('Search error:', error);
            throw new Error('Failed to search documents');
        }
    }

    generateUniqueFileName(originalName, userId) {
        const timestamp = Date.now();
        const extension = originalName.split('.').pop();
        const nameWithoutExt = originalName.replace(/\.[^/.]+$/, '');
        const sanitizedName = nameWithoutExt.replace(/[^a-zA-Z0-9]/g, '_');
        
        return `${userId}/${timestamp}_${sanitizedName}.${extension}`;
    }

    async deleteBlob(fileName) {
        try {
            const containerClient = this.blobServiceClient.getContainerClient(this.containerName);
            const blockBlobClient = containerClient.getBlockBlobClient(fileName);
            await blockBlobClient.delete();
            return true;
        } catch (error) {
            console.error('Blob deletion error:', error);
            return false;
        }
    }
}

module.exports = AzureServices;
