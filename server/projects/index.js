const { app } = require('@azure/functions');
const Database = require('../shared/database');
const AuthService = require('../shared/auth');

// Get all projects for the authenticated user
app.http('getProjects', {
    methods: ['GET'],
    authLevel: 'anonymous',
    route: 'projects',
    handler: async (request, context) => {
        try {
            const db = new Database();
            const auth = new AuthService();

            // Authenticate user
            const user = auth.authenticateRequest(request);

            // Get projects for user
            const projects = await db.getProjectsByUser(user.id);

            return {
                status: 200,
                jsonBody: {
                    projects: projects.map(project => ({
                        id: project.id,
                        name: project.name,
                        description: project.description,
                        clientName: project.client_name,
                        clientEmail: project.client_email,
                        status: project.status,
                        startDate: project.start_date,
                        endDate: project.end_date,
                        createdAt: project.created_at,
                        updatedAt: project.updated_at
                    }))
                }
            };

        } catch (error) {
            context.log.error('Get projects error:', error);
            
            if (error.message.includes('Authentication failed')) {
                return {
                    status: 401,
                    jsonBody: { error: 'Authentication required' }
                };
            }

            return {
                status: 500,
                jsonBody: { error: 'Internal server error' }
            };
        }
    }
});

// Create a new project
app.http('createProject', {
    methods: ['POST'],
    authLevel: 'anonymous',
    route: 'projects',
    handler: async (request, context) => {
        try {
            const db = new Database();
            const auth = new AuthService();

            // Authenticate user
            const user = auth.authenticateRequest(request);

            const body = await request.json();
            const { name, description, clientName, clientEmail, assignedTo, startDate, endDate } = body;

            if (!name || !clientName) {
                return {
                    status: 400,
                    jsonBody: { error: 'Project name and client name are required' }
                };
            }

            const projectData = {
                name,
                description,
                clientName,
                clientEmail,
                createdBy: user.id,
                assignedTo: assignedTo || [user.id],
                startDate,
                endDate
            };

            const project = await db.createProject(projectData);

            return {
                status: 201,
                jsonBody: {
                    message: 'Project created successfully',
                    project: {
                        id: project.id,
                        name: project.name,
                        description: project.description,
                        clientName: project.client_name,
                        clientEmail: project.client_email,
                        status: project.status,
                        startDate: project.start_date,
                        endDate: project.end_date,
                        createdAt: project.created_at
                    }
                }
            };

        } catch (error) {
            context.log.error('Create project error:', error);
            
            if (error.message.includes('Authentication failed')) {
                return {
                    status: 401,
                    jsonBody: { error: 'Authentication required' }
                };
            }

            return {
                status: 500,
                jsonBody: { error: 'Internal server error' }
            };
        }
    }
});

// Get a specific project
app.http('getProject', {
    methods: ['GET'],
    authLevel: 'anonymous',
    route: 'projects/{projectId}',
    handler: async (request, context) => {
        try {
            const db = new Database();
            const auth = new AuthService();

            // Authenticate user
            const user = auth.authenticateRequest(request);

            const projectId = request.params.projectId;

            // Get project
            const project = await db.getProjectById(projectId);
            if (!project) {
                return {
                    status: 404,
                    jsonBody: { error: 'Project not found' }
                };
            }

            // Check access
            if (!auth.canAccessProject(user, project)) {
                return {
                    status: 403,
                    jsonBody: { error: 'Access denied to this project' }
                };
            }

            // Get project documents
            const documents = await db.getDocumentsByProject(projectId);

            // Get analysis results
            const analysisResults = await db.getAnalysisResultsByProject(projectId);

            return {
                status: 200,
                jsonBody: {
                    project: {
                        id: project.id,
                        name: project.name,
                        description: project.description,
                        clientName: project.client_name,
                        clientEmail: project.client_email,
                        status: project.status,
                        startDate: project.start_date,
                        endDate: project.end_date,
                        createdAt: project.created_at,
                        updatedAt: project.updated_at
                    },
                    documents: documents.map(doc => ({
                        id: doc.id,
                        originalName: doc.original_name,
                        fileType: doc.file_type,
                        fileSize: doc.file_size,
                        status: doc.status,
                        uploadedAt: doc.created_at
                    })),
                    analysisResults: analysisResults.map(result => ({
                        id: result.id,
                        documentName: result.original_name,
                        summary: result.ai_summary,
                        redFlags: result.red_flags,
                        highlights: result.highlights,
                        confidenceScore: result.confidence_score,
                        createdAt: result.created_at
                    }))
                }
            };

        } catch (error) {
            context.log.error('Get project error:', error);
            
            if (error.message.includes('Authentication failed')) {
                return {
                    status: 401,
                    jsonBody: { error: 'Authentication required' }
                };
            }

            return {
                status: 500,
                jsonBody: { error: 'Internal server error' }
            };
        }
    }
});

// Update a project
app.http('updateProject', {
    methods: ['PUT'],
    authLevel: 'anonymous',
    route: 'projects/{projectId}',
    handler: async (request, context) => {
        try {
            const db = new Database();
            const auth = new AuthService();

            // Authenticate user
            const user = auth.authenticateRequest(request);

            const projectId = request.params.projectId;

            // Get project
            const project = await db.getProjectById(projectId);
            if (!project) {
                return {
                    status: 404,
                    jsonBody: { error: 'Project not found' }
                };
            }

            // Check if user can modify project (creator or admin)
            if (project.created_by !== user.id && user.role !== 'admin') {
                return {
                    status: 403,
                    jsonBody: { error: 'Only project creator or admin can modify project' }
                };
            }

            const body = await request.json();
            const { name, description, clientName, clientEmail, status, assignedTo, startDate, endDate } = body;

            const updates = {};
            if (name) updates.name = name;
            if (description !== undefined) updates.description = description;
            if (clientName) updates.client_name = clientName;
            if (clientEmail !== undefined) updates.client_email = clientEmail;
            if (status) updates.status = status;
            if (assignedTo) updates.assigned_to = assignedTo;
            if (startDate !== undefined) updates.start_date = startDate;
            if (endDate !== undefined) updates.end_date = endDate;

            if (Object.keys(updates).length === 0) {
                return {
                    status: 400,
                    jsonBody: { error: 'No valid fields to update' }
                };
            }

            const updatedProject = await db.updateProject(projectId, updates);

            return {
                status: 200,
                jsonBody: {
                    message: 'Project updated successfully',
                    project: {
                        id: updatedProject.id,
                        name: updatedProject.name,
                        description: updatedProject.description,
                        clientName: updatedProject.client_name,
                        clientEmail: updatedProject.client_email,
                        status: updatedProject.status,
                        startDate: updatedProject.start_date,
                        endDate: updatedProject.end_date,
                        updatedAt: updatedProject.updated_at
                    }
                }
            };

        } catch (error) {
            context.log.error('Update project error:', error);
            
            if (error.message.includes('Authentication failed')) {
                return {
                    status: 401,
                    jsonBody: { error: 'Authentication required' }
                };
            }

            return {
                status: 500,
                jsonBody: { error: 'Internal server error' }
            };
        }
    }
});
