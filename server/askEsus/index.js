const { app } = require('@azure/functions');
const Database = require('../shared/database');
const AuthService = require('../shared/auth');
const AzureServices = require('../shared/azureServices');
const OpenAIClient = require('../shared/openaiClient');

app.http('askEsus', {
    methods: ['POST'],
    authLevel: 'anonymous',
    handler: async (request, context) => {
        context.log('Ask Esus request received');

        try {
            // Initialize services
            const db = new Database();
            const auth = new AuthService();
            const azure = new AzureServices();
            const openai = new OpenAIClient();

            // Authenticate user
            const user = auth.authenticateRequest(request);

            // Get request body
            const body = await request.json();
            const { question, projectId } = body;

            if (!question || !projectId) {
                return {
                    status: 400,
                    jsonBody: { error: 'Question and project ID are required' }
                };
            }

            // Verify user can access project
            const project = await db.getProjectById(projectId);
            if (!project) {
                return {
                    status: 404,
                    jsonBody: { error: 'Project not found' }
                };
            }

            if (!auth.canAccessProject(user, project)) {
                return {
                    status: 403,
                    jsonBody: { error: 'Access denied to this project' }
                };
            }

            // Search for relevant documents using Azure Cognitive Search
            const searchResults = await azure.searchDocuments(question, projectId, 5);

            // Get analysis results for the project to provide context
            const analysisResults = await db.getAnalysisResultsByProject(projectId);

            // Build context from search results and analysis
            const context = buildContextFromResults(searchResults.results, analysisResults);

            // Get AI response from OpenAI
            const answer = await openai.askEsus(question, context, project);

            // Save chat entry to database
            const contextDocuments = searchResults.results.map(result => result.id);
            const chatData = {
                projectId: projectId,
                userId: user.id,
                question: question,
                answer: answer,
                contextDocuments: contextDocuments
            };

            const chatEntry = await db.createChatEntry(chatData);

            return {
                status: 200,
                jsonBody: {
                    answer: answer,
                    chatId: chatEntry.id,
                    contextDocuments: contextDocuments,
                    relevantDocuments: searchResults.results.map(result => ({
                        id: result.id,
                        score: result.score,
                        metadata: result.metadata
                    }))
                }
            };

        } catch (error) {
            context.log.error('Ask Esus error:', error);

            if (error.message.includes('Authentication failed')) {
                return {
                    status: 401,
                    jsonBody: { error: 'Authentication required' }
                };
            }

            return {
                status: 500,
                jsonBody: { error: 'Internal server error: ' + error.message }
            };
        }
    }
});

// Function to get chat history for a project
app.http('getChatHistory', {
    methods: ['GET'],
    authLevel: 'anonymous',
    route: 'projects/{projectId}/chat',
    handler: async (request, context) => {
        try {
            const db = new Database();
            const auth = new AuthService();

            // Authenticate user
            const user = auth.authenticateRequest(request);

            const projectId = request.params.projectId;
            const limit = parseInt(request.query.get('limit')) || 50;

            // Verify user can access project
            const project = await db.getProjectById(projectId);
            if (!project) {
                return {
                    status: 404,
                    jsonBody: { error: 'Project not found' }
                };
            }

            if (!auth.canAccessProject(user, project)) {
                return {
                    status: 403,
                    jsonBody: { error: 'Access denied to this project' }
                };
            }

            // Get chat history
            const chatHistory = await db.getChatHistory(projectId, limit);

            return {
                status: 200,
                jsonBody: {
                    chatHistory: chatHistory.map(chat => ({
                        id: chat.id,
                        question: chat.question,
                        answer: chat.answer,
                        user: {
                            firstName: chat.first_name,
                            lastName: chat.last_name
                        },
                        contextDocuments: chat.context_documents,
                        createdAt: chat.created_at
                    }))
                }
            };

        } catch (error) {
            context.log.error('Get chat history error:', error);

            if (error.message.includes('Authentication failed')) {
                return {
                    status: 401,
                    jsonBody: { error: 'Authentication required' }
                };
            }

            return {
                status: 500,
                jsonBody: { error: 'Internal server error' }
            };
        }
    }
});

// Function to get suggested questions based on project analysis
app.http('getSuggestedQuestions', {
    methods: ['GET'],
    authLevel: 'anonymous',
    route: 'projects/{projectId}/suggested-questions',
    handler: async (request, context) => {
        try {
            const db = new Database();
            const auth = new AuthService();

            // Authenticate user
            const user = auth.authenticateRequest(request);

            const projectId = request.params.projectId;

            // Verify user can access project
            const project = await db.getProjectById(projectId);
            if (!project) {
                return {
                    status: 404,
                    jsonBody: { error: 'Project not found' }
                };
            }

            if (!auth.canAccessProject(user, project)) {
                return {
                    status: 403,
                    jsonBody: { error: 'Access denied to this project' }
                };
            }

            // Get analysis results to generate relevant questions
            const analysisResults = await db.getAnalysisResultsByProject(projectId);

            const suggestedQuestions = generateSuggestedQuestions(analysisResults, project);

            return {
                status: 200,
                jsonBody: {
                    suggestedQuestions: suggestedQuestions
                }
            };

        } catch (error) {
            context.log.error('Get suggested questions error:', error);
            return {
                status: 500,
                jsonBody: { error: 'Internal server error' }
            };
        }
    }
});

// Helper function to build context from search results and analysis
function buildContextFromResults(searchResults, analysisResults) {
    const context = [];

    // Add analysis results as context
    analysisResults.forEach(analysis => {
        context.push({
            summary: analysis.ai_summary,
            redFlags: analysis.red_flags,
            highlights: analysis.highlights,
            documentName: analysis.original_name,
            fileType: analysis.file_type
        });
    });

    // Add search results as additional context
    searchResults.forEach(result => {
        context.push({
            content: result.content.substring(0, 1000), // Limit content length
            metadata: result.metadata,
            score: result.score
        });
    });

    return context;
}

// Helper function to generate suggested questions based on analysis results
function generateSuggestedQuestions(analysisResults, project) {
    const questions = [
        `What are the key financial highlights for ${project.client_name}?`,
        'Are there any compliance issues I should be aware of?',
        'What are the main risk factors identified in the documents?',
        'Can you summarize the financial performance?'
    ];

    // Add specific questions based on red flags found
    const allRedFlags = analysisResults.flatMap(result => result.red_flags || []);
    if (allRedFlags.length > 0) {
        questions.push('What are the most critical red flags found?');
        questions.push('How should I address the identified issues?');
    }

    // Add questions based on document types
    const hasInvoices = analysisResults.some(result =>
        result.original_name.toLowerCase().includes('invoice')
    );
    if (hasInvoices) {
        questions.push('What is the total invoice amount for this period?');
        questions.push('Are there any invoice discrepancies?');
    }

    const hasFinancialStatements = analysisResults.some(result =>
        result.original_name.toLowerCase().includes('financial') ||
        result.original_name.toLowerCase().includes('statement')
    );
    if (hasFinancialStatements) {
        questions.push('What is the company\'s current financial position?');
        questions.push('How does this compare to previous periods?');
    }

    return questions.slice(0, 8); // Return max 8 questions
}