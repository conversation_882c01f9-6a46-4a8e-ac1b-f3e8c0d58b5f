const { app } = require('@azure/functions');
const Database = require('../shared/database');
const AuthService = require('../shared/auth');
const AzureServices = require('../shared/azureServices');
const OpenAIClient = require('../shared/openaiClient');
const jsPDF = require('jspdf');

app.http('generateReport', {
    methods: ['POST'],
    authLevel: 'anonymous',
    handler: async (request, context) => {
        context.log('Generate report request received');

        try {
            // Initialize services
            const db = new Database();
            const auth = new AuthService();
            const azure = new AzureServices();
            const openai = new OpenAIClient();

            // Authenticate user
            const user = auth.authenticateRequest(request);

            // Get request body
            const body = await request.json();
            const { projectId, reportName, includeCharts = false } = body;

            if (!projectId) {
                return {
                    status: 400,
                    jsonBody: { error: 'Project ID is required' }
                };
            }

            // Verify user can access project
            const project = await db.getProjectById(projectId);
            if (!project) {
                return {
                    status: 404,
                    jsonBody: { error: 'Project not found' }
                };
            }

            if (!auth.canAccessProject(user, project)) {
                return {
                    status: 403,
                    jsonBody: { error: 'Access denied to this project' }
                };
            }

            // Get all analysis results for the project
            const analysisResults = await db.getAnalysisResultsByProject(projectId);

            if (analysisResults.length === 0) {
                return {
                    status: 400,
                    jsonBody: { error: 'No analyzed documents found for this project' }
                };
            }

            // Get project documents
            const documents = await db.getDocumentsByProject(projectId);

            // Generate AI summary for the entire project
            const projectSummary = await openai.generateReportSummary(project, analysisResults);

            // Create report data structure
            const reportData = {
                project: {
                    name: project.name,
                    clientName: project.client_name,
                    description: project.description,
                    startDate: project.start_date,
                    endDate: project.end_date,
                    status: project.status
                },
                summary: projectSummary,
                documents: documents.map(doc => ({
                    id: doc.id,
                    name: doc.original_name,
                    type: doc.file_type,
                    size: doc.file_size,
                    uploadedAt: doc.created_at,
                    status: doc.status
                })),
                analysis: analysisResults.map(result => ({
                    documentName: result.original_name,
                    summary: result.ai_summary,
                    redFlags: result.red_flags,
                    highlights: result.highlights,
                    confidenceScore: result.confidence_score,
                    extractedData: JSON.parse(result.extracted_data)
                })),
                statistics: generateStatistics(analysisResults),
                generatedAt: new Date().toISOString(),
                generatedBy: {
                    id: user.id,
                    name: `${user.firstName} ${user.lastName}`,
                    email: user.email
                }
            };

            // Generate PDF report
            const pdfBuffer = await generatePDFReport(reportData, includeCharts);

            // Upload PDF to Azure Blob Storage
            const reportFileName = `reports/${projectId}/${Date.now()}_${reportName || 'audit_report'}.pdf`;
            const uploadResult = await azure.uploadToBlob(
                reportFileName,
                pdfBuffer,
                'application/pdf'
            );

            // Save report record to database
            const auditReportData = {
                projectId: projectId,
                generatedBy: user.id,
                reportName: reportName || 'Audit Report',
                reportData: JSON.stringify(reportData),
                pdfUrl: uploadResult.url
            };

            const auditReport = await db.createAuditReport(auditReportData);

            return {
                status: 200,
                jsonBody: {
                    message: 'Report generated successfully',
                    report: {
                        id: auditReport.id,
                        name: auditReport.report_name,
                        pdfUrl: auditReport.pdf_url,
                        generatedAt: auditReport.created_at,
                        summary: projectSummary.substring(0, 200) + '...'
                    }
                }
            };

        } catch (error) {
            context.log.error('Generate report error:', error);

            if (error.message.includes('Authentication failed')) {
                return {
                    status: 401,
                    jsonBody: { error: 'Authentication required' }
                };
            }

            return {
                status: 500,
                jsonBody: { error: 'Internal server error: ' + error.message }
            };
        }
    }
});

// Function to get all reports for a project
app.http('getReports', {
    methods: ['GET'],
    authLevel: 'anonymous',
    route: 'projects/{projectId}/reports',
    handler: async (request, context) => {
        try {
            const db = new Database();
            const auth = new AuthService();

            // Authenticate user
            const user = auth.authenticateRequest(request);

            const projectId = request.params.projectId;

            // Verify user can access project
            const project = await db.getProjectById(projectId);
            if (!project) {
                return {
                    status: 404,
                    jsonBody: { error: 'Project not found' }
                };
            }

            if (!auth.canAccessProject(user, project)) {
                return {
                    status: 403,
                    jsonBody: { error: 'Access denied to this project' }
                };
            }

            // Get all reports for the project
            const reports = await db.getAuditReportsByProject(projectId);

            return {
                status: 200,
                jsonBody: {
                    reports: reports.map(report => ({
                        id: report.id,
                        name: report.report_name,
                        pdfUrl: report.pdf_url,
                        status: report.status,
                        generatedAt: report.created_at,
                        generatedBy: report.generated_by
                    }))
                }
            };

        } catch (error) {
            context.log.error('Get reports error:', error);
            return {
                status: 500,
                jsonBody: { error: 'Internal server error' }
            };
        }
    }
});

// Helper function to generate statistics from analysis results
function generateStatistics(analysisResults) {
    const stats = {
        totalDocuments: analysisResults.length,
        averageConfidenceScore: 0,
        totalRedFlags: 0,
        totalHighlights: 0,
        documentTypes: {},
        processingTime: 0
    };

    let totalConfidence = 0;
    let totalProcessingTime = 0;

    analysisResults.forEach(result => {
        // Calculate confidence score
        if (result.confidence_score) {
            totalConfidence += parseFloat(result.confidence_score);
        }

        // Count red flags and highlights
        stats.totalRedFlags += (result.red_flags || []).length;
        stats.totalHighlights += (result.highlights || []).length;

        // Count document types
        const fileType = result.file_type || 'unknown';
        stats.documentTypes[fileType] = (stats.documentTypes[fileType] || 0) + 1;

        // Sum processing time
        if (result.processing_time_ms) {
            totalProcessingTime += result.processing_time_ms;
        }
    });

    stats.averageConfidenceScore = analysisResults.length > 0 ?
        (totalConfidence / analysisResults.length).toFixed(2) : 0;
    stats.processingTime = totalProcessingTime;

    return stats;
}

// Helper function to generate PDF report
async function generatePDFReport(reportData, includeCharts) {
    const doc = new jsPDF();
    let yPosition = 20;

    // Title
    doc.setFontSize(20);
    doc.text('Audit Report', 20, yPosition);
    yPosition += 15;

    // Project Information
    doc.setFontSize(16);
    doc.text('Project Information', 20, yPosition);
    yPosition += 10;

    doc.setFontSize(12);
    doc.text(`Project: ${reportData.project.name}`, 20, yPosition);
    yPosition += 7;
    doc.text(`Client: ${reportData.project.clientName}`, 20, yPosition);
    yPosition += 7;
    doc.text(`Status: ${reportData.project.status}`, 20, yPosition);
    yPosition += 7;
    doc.text(`Generated: ${new Date(reportData.generatedAt).toLocaleDateString()}`, 20, yPosition);
    yPosition += 15;

    // Executive Summary
    doc.setFontSize(16);
    doc.text('Executive Summary', 20, yPosition);
    yPosition += 10;

    doc.setFontSize(10);
    const summaryLines = doc.splitTextToSize(reportData.summary, 170);
    doc.text(summaryLines, 20, yPosition);
    yPosition += summaryLines.length * 5 + 10;

    // Statistics
    if (yPosition > 250) {
        doc.addPage();
        yPosition = 20;
    }

    doc.setFontSize(16);
    doc.text('Statistics', 20, yPosition);
    yPosition += 10;

    doc.setFontSize(12);
    doc.text(`Total Documents: ${reportData.statistics.totalDocuments}`, 20, yPosition);
    yPosition += 7;
    doc.text(`Average Confidence Score: ${reportData.statistics.averageConfidenceScore}`, 20, yPosition);
    yPosition += 7;
    doc.text(`Total Red Flags: ${reportData.statistics.totalRedFlags}`, 20, yPosition);
    yPosition += 7;
    doc.text(`Total Highlights: ${reportData.statistics.totalHighlights}`, 20, yPosition);
    yPosition += 15;

    // Document Analysis
    doc.setFontSize(16);
    doc.text('Document Analysis', 20, yPosition);
    yPosition += 10;

    reportData.analysis.forEach((analysis, index) => {
        if (yPosition > 250) {
            doc.addPage();
            yPosition = 20;
        }

        doc.setFontSize(14);
        doc.text(`${index + 1}. ${analysis.documentName}`, 20, yPosition);
        yPosition += 8;

        doc.setFontSize(10);
        const summaryLines = doc.splitTextToSize(analysis.summary, 170);
        doc.text(summaryLines, 25, yPosition);
        yPosition += summaryLines.length * 4 + 5;

        if (analysis.redFlags && analysis.redFlags.length > 0) {
            doc.setFontSize(10);
            doc.text('Red Flags:', 25, yPosition);
            yPosition += 5;
            analysis.redFlags.forEach(flag => {
                const flagLines = doc.splitTextToSize(`• ${flag}`, 165);
                doc.text(flagLines, 30, yPosition);
                yPosition += flagLines.length * 4;
            });
            yPosition += 3;
        }

        if (analysis.highlights && analysis.highlights.length > 0) {
            doc.setFontSize(10);
            doc.text('Highlights:', 25, yPosition);
            yPosition += 5;
            analysis.highlights.forEach(highlight => {
                const highlightLines = doc.splitTextToSize(`• ${highlight}`, 165);
                doc.text(highlightLines, 30, yPosition);
                yPosition += highlightLines.length * 4;
            });
            yPosition += 5;
        }

        yPosition += 5;
    });

    return Buffer.from(doc.output('arraybuffer'));
}