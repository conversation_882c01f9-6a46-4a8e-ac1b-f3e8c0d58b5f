const { app } = require('@azure/functions');
const Database = require('../shared/database');
const AuthService = require('../shared/auth');
const AzureServices = require('../shared/azureServices');
const OpenAIClient = require('../shared/openaiClient');

app.http('analyzeDoc', {
    methods: ['POST'],
    authLevel: 'anonymous',
    handler: async (request, context) => {
        context.log('Document analysis request received');

        try {
            // Initialize services
            const db = new Database();
            const auth = new AuthService();
            const azure = new AzureServices();
            const openai = new OpenAIClient();

            // Authenticate user
            const user = auth.authenticateRequest(request);

            // Get request body
            const body = await request.json();
            const { documentId } = body;

            if (!documentId) {
                return {
                    status: 400,
                    jsonBody: { error: 'Document ID is required' }
                };
            }

            // Get document from database
            const document = await db.query('SELECT * FROM documents WHERE id = $1', [documentId]);
            if (!document.rows.length) {
                return {
                    status: 404,
                    jsonBody: { error: 'Document not found' }
                };
            }

            const doc = document.rows[0];

            // Get project to verify access
            const project = await db.getProjectById(doc.project_id);
            if (!project) {
                return {
                    status: 404,
                    jsonBody: { error: 'Project not found' }
                };
            }

            if (!auth.canAccessProject(user, project)) {
                return {
                    status: 403,
                    jsonBody: { error: 'Access denied to this project' }
                };
            }

            // Update document status to processing
            await db.updateDocumentStatus(documentId, 'processing');

            const startTime = Date.now();

            try {
                // Determine document type for Form Recognizer
                let documentType = 'prebuilt-document';
                if (doc.file_type === 'application/pdf') {
                    // Try to detect if it's an invoice or receipt based on filename
                    const filename = doc.original_name.toLowerCase();
                    if (filename.includes('invoice')) {
                        documentType = 'prebuilt-invoice';
                    } else if (filename.includes('receipt')) {
                        documentType = 'prebuilt-receipt';
                    }
                }

                // Analyze document with Azure Form Recognizer
                context.log(`Analyzing document with Form Recognizer: ${doc.blob_url}`);
                const extractedData = await azure.analyzeDocument(doc.blob_url, documentType);

                // Analyze with OpenAI
                context.log('Analyzing extracted data with OpenAI');
                const aiAnalysis = await openai.analyzeDocument(extractedData, getDocumentCategory(doc.file_type));

                const processingTime = Date.now() - startTime;

                // Save analysis results to database
                const analysisData = {
                    documentId: documentId,
                    extractedData: JSON.stringify(extractedData),
                    aiSummary: aiAnalysis.summary,
                    redFlags: aiAnalysis.redFlags,
                    highlights: aiAnalysis.highlights,
                    confidenceScore: aiAnalysis.confidenceScore,
                    processingTime: processingTime
                };

                const analysisResult = await db.createAnalysisResult(analysisData);

                // Index document for search
                const searchMetadata = {
                    project_id: doc.project_id,
                    document_id: documentId,
                    file_type: doc.file_type,
                    original_name: doc.original_name,
                    uploaded_by: doc.uploaded_by
                };

                await azure.indexDocument(
                    documentId,
                    `${extractedData.content} ${aiAnalysis.summary}`,
                    searchMetadata
                );

                // Update document status to analyzed
                await db.updateDocumentStatus(documentId, 'analyzed');

                return {
                    status: 200,
                    jsonBody: {
                        message: 'Document analyzed successfully',
                        analysis: {
                            id: analysisResult.id,
                            summary: aiAnalysis.summary,
                            redFlags: aiAnalysis.redFlags,
                            highlights: aiAnalysis.highlights,
                            confidenceScore: aiAnalysis.confidenceScore,
                            processingTimeMs: processingTime,
                            extractedData: extractedData
                        }
                    }
                };

            } catch (analysisError) {
                context.log.error('Analysis error:', analysisError);

                // Update document status to error
                await db.updateDocumentStatus(documentId, 'error');

                return {
                    status: 500,
                    jsonBody: {
                        error: 'Document analysis failed',
                        details: analysisError.message
                    }
                };
            }

        } catch (error) {
            context.log.error('Analyze document error:', error);

            if (error.message.includes('Authentication failed')) {
                return {
                    status: 401,
                    jsonBody: { error: 'Authentication required' }
                };
            }

            return {
                status: 500,
                jsonBody: { error: 'Internal server error: ' + error.message }
            };
        }
    }
});

// Helper function to determine document category for AI analysis
function getDocumentCategory(fileType) {
    const categoryMap = {
        'application/pdf': 'financial',
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': 'spreadsheet',
        'application/vnd.ms-excel': 'spreadsheet',
        'application/vnd.openxmlformats-officedocument.wordprocessingml.document': 'document',
        'application/msword': 'document',
        'text/csv': 'data'
    };

    return categoryMap[fileType] || 'general';
}

// Function to get analysis results for a document
app.http('getAnalysis', {
    methods: ['GET'],
    authLevel: 'anonymous',
    route: 'documents/{documentId}/analysis',
    handler: async (request, context) => {
        try {
            const db = new Database();
            const auth = new AuthService();

            // Authenticate user
            const user = auth.authenticateRequest(request);

            const documentId = request.params.documentId;

            // Get document and verify access
            const document = await db.query('SELECT * FROM documents WHERE id = $1', [documentId]);
            if (!document.rows.length) {
                return {
                    status: 404,
                    jsonBody: { error: 'Document not found' }
                };
            }

            const doc = document.rows[0];
            const project = await db.getProjectById(doc.project_id);

            if (!auth.canAccessProject(user, project)) {
                return {
                    status: 403,
                    jsonBody: { error: 'Access denied' }
                };
            }

            // Get analysis results
            const analysis = await db.getAnalysisResultsByDocument(documentId);

            if (!analysis) {
                return {
                    status: 404,
                    jsonBody: { error: 'Analysis not found' }
                };
            }

            return {
                status: 200,
                jsonBody: {
                    analysis: {
                        id: analysis.id,
                        summary: analysis.ai_summary,
                        redFlags: analysis.red_flags,
                        highlights: analysis.highlights,
                        confidenceScore: analysis.confidence_score,
                        processingTimeMs: analysis.processing_time_ms,
                        extractedData: JSON.parse(analysis.extracted_data),
                        createdAt: analysis.created_at
                    }
                }
            };

        } catch (error) {
            context.log.error('Get analysis error:', error);
            return {
                status: 500,
                jsonBody: { error: 'Internal server error' }
            };
        }
    }
});