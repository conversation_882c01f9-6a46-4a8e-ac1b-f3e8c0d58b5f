{"name": "esus-audit-ai-server", "version": "1.0.0", "description": "Azure Functions backend for Esus Audit AI", "main": "index.js", "scripts": {"start": "func start", "test": "echo \"No tests yet\"", "deploy": "func azure functionapp publish esus-audit-ai-functions"}, "dependencies": {"@azure/functions": "^4.0.0", "@azure/storage-blob": "^12.17.0", "@azure/ai-form-recognizer": "^5.0.0", "@azure/search-documents": "^12.0.0", "openai": "^4.20.1", "pg": "^8.11.3", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "multer": "^1.4.5-lts.1", "uuid": "^9.0.1", "pdf-parse": "^1.1.1", "mammoth": "^1.6.0", "xlsx": "^0.18.5", "jspdf": "^2.5.1", "html-pdf": "^3.0.1", "dotenv": "^16.3.1", "cors": "^2.8.5", "express": "^4.18.2"}, "devDependencies": {"@azure/functions-core-tools": "^4.0.5455"}}