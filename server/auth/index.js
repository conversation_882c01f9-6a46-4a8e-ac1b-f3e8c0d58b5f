const { app } = require('@azure/functions');
const Database = require('../shared/database');
const AuthService = require('../shared/auth');

// User registration
app.http('register', {
    methods: ['POST'],
    authLevel: 'anonymous',
    handler: async (request, context) => {
        context.log('User registration request received');

        try {
            const db = new Database();
            const auth = new AuthService();

            const body = await request.json();
            const { email, password, firstName, lastName, company, role = 'auditor' } = body;

            // Validate input
            if (!email || !password || !firstName || !lastName) {
                return {
                    status: 400,
                    jsonBody: { error: 'Email, password, first name, and last name are required' }
                };
            }

            if (!auth.validateEmail(email)) {
                return {
                    status: 400,
                    jsonBody: { error: 'Invalid email format' }
                };
            }

            if (!auth.validatePassword(password)) {
                return {
                    status: 400,
                    jsonBody: { error: 'Password must be at least 8 characters with uppercase, lowercase, and number' }
                };
            }

            // Check if user already exists
            const existingUser = await db.getUserByEmail(email);
            if (existingUser) {
                return {
                    status: 409,
                    jsonBody: { error: 'User with this email already exists' }
                };
            }

            // Hash password
            const passwordHash = await auth.hashPassword(password);

            // Create user
            const userData = {
                email,
                passwordHash,
                firstName,
                lastName,
                role,
                company
            };

            const user = await db.createUser(userData);
            const token = auth.generateToken(user);

            return {
                status: 201,
                jsonBody: {
                    message: 'User registered successfully',
                    user: auth.sanitizeUser(user),
                    token
                }
            };

        } catch (error) {
            context.log.error('Registration error:', error);
            return {
                status: 500,
                jsonBody: { error: 'Internal server error' }
            };
        }
    }
});

// User login
app.http('login', {
    methods: ['POST'],
    authLevel: 'anonymous',
    handler: async (request, context) => {
        context.log('User login request received');

        try {
            const db = new Database();
            const auth = new AuthService();

            const body = await request.json();
            const { email, password } = body;

            if (!email || !password) {
                return {
                    status: 400,
                    jsonBody: { error: 'Email and password are required' }
                };
            }

            // Get user by email
            const user = await db.getUserByEmail(email);
            if (!user) {
                return {
                    status: 401,
                    jsonBody: { error: 'Invalid credentials' }
                };
            }

            // Verify password
            const isValidPassword = await auth.comparePassword(password, user.password_hash);
            if (!isValidPassword) {
                return {
                    status: 401,
                    jsonBody: { error: 'Invalid credentials' }
                };
            }

            // Generate token
            const token = auth.generateToken(user);

            return {
                status: 200,
                jsonBody: {
                    message: 'Login successful',
                    user: auth.sanitizeUser(user),
                    token
                }
            };

        } catch (error) {
            context.log.error('Login error:', error);
            return {
                status: 500,
                jsonBody: { error: 'Internal server error' }
            };
        }
    }
});

// Verify token and get user info
app.http('verify', {
    methods: ['GET'],
    authLevel: 'anonymous',
    handler: async (request, context) => {
        try {
            const db = new Database();
            const auth = new AuthService();

            // Authenticate user
            const user = auth.authenticateRequest(request);

            // Get fresh user data from database
            const userData = await db.getUserById(user.id);
            if (!userData) {
                return {
                    status: 404,
                    jsonBody: { error: 'User not found' }
                };
            }

            return {
                status: 200,
                jsonBody: {
                    user: auth.sanitizeUser(userData)
                }
            };

        } catch (error) {
            context.log.error('Verify token error:', error);
            
            if (error.message.includes('Authentication failed')) {
                return {
                    status: 401,
                    jsonBody: { error: 'Invalid or expired token' }
                };
            }

            return {
                status: 500,
                jsonBody: { error: 'Internal server error' }
            };
        }
    }
});

// Update user profile
app.http('updateProfile', {
    methods: ['PUT'],
    authLevel: 'anonymous',
    route: 'auth/profile',
    handler: async (request, context) => {
        try {
            const db = new Database();
            const auth = new AuthService();

            // Authenticate user
            const user = auth.authenticateRequest(request);

            const body = await request.json();
            const { firstName, lastName, company } = body;

            const updates = {};
            if (firstName) updates.first_name = firstName;
            if (lastName) updates.last_name = lastName;
            if (company) updates.company = company;

            if (Object.keys(updates).length === 0) {
                return {
                    status: 400,
                    jsonBody: { error: 'No valid fields to update' }
                };
            }

            const updatedUser = await db.updateProject(user.id, updates);

            return {
                status: 200,
                jsonBody: {
                    message: 'Profile updated successfully',
                    user: auth.sanitizeUser(updatedUser)
                }
            };

        } catch (error) {
            context.log.error('Update profile error:', error);
            return {
                status: 500,
                jsonBody: { error: 'Internal server error' }
            };
        }
    }
});
