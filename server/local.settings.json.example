{"IsEncrypted": false, "Values": {"AzureWebJobsStorage": "UseDevelopmentStorage=true", "FUNCTIONS_WORKER_RUNTIME": "node", "NODE_ENV": "development", "DATABASE_URL": "postgresql://username:password@localhost:5432/esus_audit_ai_dev", "AZURE_STORAGE_CONNECTION_STRING": "DefaultEndpointsProtocol=https;AccountName=your_storage_account;AccountKey=your_storage_key;EndpointSuffix=core.windows.net", "AZURE_STORAGE_CONTAINER_NAME": "documents", "AZURE_FORM_RECOGNIZER_ENDPOINT": "https://your-form-recognizer.cognitiveservices.azure.com/", "AZURE_FORM_RECOGNIZER_KEY": "your_form_recognizer_key_here", "AZURE_OPENAI_ENDPOINT": "https://your-openai.openai.azure.com/", "AZURE_OPENAI_API_KEY": "your_openai_key_here", "AZURE_OPENAI_DEPLOYMENT_NAME": "gpt-4-turbo", "AZURE_OPENAI_API_VERSION": "2024-02-15-preview", "AZURE_SEARCH_ENDPOINT": "https://your-search-service.search.windows.net", "AZURE_SEARCH_API_KEY": "your_search_key_here", "AZURE_SEARCH_INDEX_NAME": "esus-audit-documents", "JWT_SECRET": "your_super_secret_jwt_key_for_development_only", "JWT_EXPIRES_IN": "24h"}, "Host": {"CORS": "*", "CORSCredentials": false}}