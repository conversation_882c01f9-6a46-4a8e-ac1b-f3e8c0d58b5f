const { app } = require('@azure/functions');
const multer = require('multer');
const Database = require('../shared/database');
const AuthService = require('../shared/auth');
const AzureServices = require('../shared/azureServices');

// Configure multer for memory storage
const upload = multer({
    storage: multer.memoryStorage(),
    limits: {
        fileSize: 50 * 1024 * 1024 // 50MB limit
    },
    fileFilter: (req, file, cb) => {
        const allowedTypes = ['application/pdf', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
                             'application/vnd.ms-excel', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
                             'application/msword', 'text/csv'];

        if (allowedTypes.includes(file.mimetype)) {
            cb(null, true);
        } else {
            cb(new Error('Invalid file type. Only PDF, Excel, Word, and CSV files are allowed.'), false);
        }
    }
});

app.http('uploadDoc', {
    methods: ['POST'],
    authLevel: 'anonymous',
    handler: async (request, context) => {
        context.log('Document upload request received');

        try {
            // Initialize services
            const db = new Database();
            const auth = new AuthService();
            const azure = new AzureServices();

            // Authenticate user
            const user = auth.authenticateRequest(request);

            // Parse multipart form data
            const contentType = request.headers.get('content-type');
            if (!contentType || !contentType.includes('multipart/form-data')) {
                return {
                    status: 400,
                    jsonBody: { error: 'Content-Type must be multipart/form-data' }
                };
            }

            // Get request body as buffer
            const body = await request.arrayBuffer();
            const buffer = Buffer.from(body);

            // Parse form data manually (simplified version)
            const formData = await parseMultipartData(buffer, contentType);

            if (!formData.file) {
                return {
                    status: 400,
                    jsonBody: { error: 'No file uploaded' }
                };
            }

            const { projectId } = formData.fields;
            if (!projectId) {
                return {
                    status: 400,
                    jsonBody: { error: 'Project ID is required' }
                };
            }

            // Verify user can access project
            const project = await db.getProjectById(projectId);
            if (!project) {
                return {
                    status: 404,
                    jsonBody: { error: 'Project not found' }
                };
            }

            if (!auth.canAccessProject(user, project)) {
                return {
                    status: 403,
                    jsonBody: { error: 'Access denied to this project' }
                };
            }

            // Generate unique filename
            const uniqueFileName = azure.generateUniqueFileName(formData.file.filename, user.id);

            // Upload to Azure Blob Storage
            const uploadResult = await azure.uploadToBlob(
                uniqueFileName,
                formData.file.buffer,
                formData.file.mimetype
            );

            // Save document record to database
            const documentData = {
                projectId: projectId,
                uploadedBy: user.id,
                originalName: formData.file.filename,
                filePath: uniqueFileName,
                fileSize: formData.file.buffer.length,
                fileType: formData.file.mimetype,
                blobUrl: uploadResult.url
            };

            const document = await db.createDocument(documentData);

            // Trigger document analysis (async)
            triggerDocumentAnalysis(document.id, uploadResult.url, formData.file.mimetype);

            return {
                status: 200,
                jsonBody: {
                    message: 'Document uploaded successfully',
                    document: {
                        id: document.id,
                        originalName: document.original_name,
                        fileSize: document.file_size,
                        fileType: document.file_type,
                        status: document.status,
                        uploadedAt: document.created_at
                    }
                }
            };

        } catch (error) {
            context.log.error('Upload error:', error);

            if (error.message.includes('Authentication failed')) {
                return {
                    status: 401,
                    jsonBody: { error: 'Authentication required' }
                };
            }

            return {
                status: 500,
                jsonBody: { error: 'Internal server error: ' + error.message }
            };
        }
    }
});

// Helper function to parse multipart form data
async function parseMultipartData(buffer, contentType) {
    // Extract boundary from content type
    const boundary = contentType.split('boundary=')[1];
    if (!boundary) {
        throw new Error('No boundary found in content type');
    }

    const parts = buffer.toString('binary').split(`--${boundary}`);
    const formData = { fields: {}, file: null };

    for (const part of parts) {
        if (part.includes('Content-Disposition')) {
            const lines = part.split('\r\n');
            const dispositionLine = lines.find(line => line.includes('Content-Disposition'));

            if (dispositionLine.includes('filename=')) {
                // This is a file
                const nameMatch = dispositionLine.match(/name="([^"]+)"/);
                const filenameMatch = dispositionLine.match(/filename="([^"]+)"/);

                if (nameMatch && filenameMatch) {
                    const contentTypeMatch = part.match(/Content-Type: ([^\r\n]+)/);
                    const contentType = contentTypeMatch ? contentTypeMatch[1] : 'application/octet-stream';

                    // Find the start of file content (after double CRLF)
                    const contentStart = part.indexOf('\r\n\r\n') + 4;
                    const contentEnd = part.lastIndexOf('\r\n');
                    const fileContent = part.substring(contentStart, contentEnd);

                    formData.file = {
                        fieldname: nameMatch[1],
                        filename: filenameMatch[1],
                        mimetype: contentType,
                        buffer: Buffer.from(fileContent, 'binary')
                    };
                }
            } else {
                // This is a regular field
                const nameMatch = dispositionLine.match(/name="([^"]+)"/);
                if (nameMatch) {
                    const contentStart = part.indexOf('\r\n\r\n') + 4;
                    const contentEnd = part.lastIndexOf('\r\n');
                    const value = part.substring(contentStart, contentEnd);
                    formData.fields[nameMatch[1]] = value;
                }
            }
        }
    }

    return formData;
}

// Async function to trigger document analysis
async function triggerDocumentAnalysis(documentId, blobUrl, fileType) {
    try {
        // This would typically be done via a queue or another Azure Function
        // For now, we'll make a direct call to the analyze function
        const analyzeUrl = `${process.env.API_BASE_URL}/analyzeDoc`;

        // In a real implementation, you'd use Azure Service Bus or Storage Queue
        // to trigger the analysis asynchronously
        console.log(`Document ${documentId} queued for analysis`);

    } catch (error) {
        console.error('Failed to trigger analysis:', error);
    }
}