-- <PERSON>sus Audit AI Database Initialization
-- This file sets up the initial database structure and essential data

-- Create initial admin user (password should be changed on first login)
-- Default password: 'EsusAdmin2024!' (hashed with bcrypt)
INSERT INTO users (email, password_hash, first_name, last_name, role, company, is_active) VALUES
('<EMAIL>', '$2a$12$LQv3c1yqBwEHFqHALGMJ4.VQVn5UVUFMHDsLaeEay7NCWDyDq7/1e', 'System', 'Administrator', 'admin', 'Esus Audit AI', true)
ON CONFLICT (email) DO NOTHING;

-- Create indexes for better performance (if not already created in schema)
CREATE INDEX IF NOT EXISTS idx_users_active ON users(is_active);
CREATE INDEX IF NOT EXISTS idx_projects_client ON projects(client_name);
CREATE INDEX IF NOT EXISTS idx_documents_uploaded_by ON documents(uploaded_by);
CREATE INDEX IF NOT EXISTS idx_analysis_confidence ON analysis_results(confidence_score);

-- Insert application settings/configuration
CREATE TABLE IF NOT EXISTS app_settings (
    key VARCHAR(255) PRIMARY KEY,
    value TEXT NOT NULL,
    description TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

INSERT INTO app_settings (key, value, description) VALUES
('app_version', '1.0.0', 'Current application version'),
('max_file_size_mb', '50', 'Maximum file upload size in MB'),
('supported_file_types', 'pdf,xlsx,xls,docx,doc,csv', 'Comma-separated list of supported file extensions'),
('ai_confidence_threshold', '0.7', 'Minimum confidence score for AI analysis results'),
('session_timeout_hours', '24', 'User session timeout in hours'),
('enable_email_notifications', 'true', 'Enable email notifications for users'),
('maintenance_mode', 'false', 'Application maintenance mode flag')
ON CONFLICT (key) DO NOTHING;