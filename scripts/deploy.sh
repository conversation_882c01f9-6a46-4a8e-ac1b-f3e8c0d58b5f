#!/bin/bash

# Esus Audit AI - Deployment Script
# This script deploys the application to Azure

set -e

echo "🚀 Starting Esus Audit AI deployment..."

# Check if Azure CLI is installed
if ! command -v az &> /dev/null; then
    echo "❌ Azure CLI is not installed. Please install it first."
    exit 1
fi

# Check if logged in to Azure
if ! az account show &> /dev/null; then
    echo "❌ Not logged in to Azure. Please run 'az login' first."
    exit 1
fi

# Configuration
RESOURCE_GROUP="esus-audit-ai-rg"
LOCATION="eastus"
FUNCTION_APP_NAME="esus-audit-ai-functions"
STORAGE_ACCOUNT="esusauditaistorage"
STATIC_WEB_APP_NAME="esus-audit-ai-frontend"

echo "📋 Configuration:"
echo "  Resource Group: $RESOURCE_GROUP"
echo "  Location: $LOCATION"
echo "  Function App: $FUNCTION_APP_NAME"
echo "  Storage Account: $STORAGE_ACCOUNT"
echo "  Static Web App: $STATIC_WEB_APP_NAME"

# Create resource group
echo "🏗️  Creating resource group..."
az group create --name $RESOURCE_GROUP --location $LOCATION

# Create storage account
echo "💾 Creating storage account..."
az storage account create \
  --name $STORAGE_ACCOUNT \
  --resource-group $RESOURCE_GROUP \
  --location $LOCATION \
  --sku Standard_LRS

# Create function app
echo "⚡ Creating Azure Functions app..."
az functionapp create \
  --resource-group $RESOURCE_GROUP \
  --consumption-plan-location $LOCATION \
  --runtime node \
  --runtime-version 18 \
  --functions-version 4 \
  --name $FUNCTION_APP_NAME \
  --storage-account $STORAGE_ACCOUNT

# Build and deploy backend
echo "🔧 Building and deploying backend..."
cd server
npm install
func azure functionapp publish $FUNCTION_APP_NAME
cd ..

# Build frontend
echo "🎨 Building frontend..."
cd client
npm install
npm run build
cd ..

# Deploy frontend to Azure Static Web Apps
echo "🌐 Deploying frontend..."
az staticwebapp create \
  --name $STATIC_WEB_APP_NAME \
  --resource-group $RESOURCE_GROUP \
  --source https://github.com/your-username/esus-audit-ai \
  --location $LOCATION \
  --branch main \
  --app-location "/client" \
  --output-location "dist"

echo "✅ Deployment completed successfully!"
echo ""
echo "🔗 Next steps:"
echo "1. Configure environment variables in Azure Portal"
echo "2. Set up Azure OpenAI, Form Recognizer, and Cognitive Search services"
echo "3. Update connection strings and API keys"
echo "4. Set up PostgreSQL database"
echo "5. Run database migrations"
echo ""
echo "📱 Access your application:"
echo "  Frontend: https://$STATIC_WEB_APP_NAME.azurestaticapps.net"
echo "  Backend: https://$FUNCTION_APP_NAME.azurewebsites.net"