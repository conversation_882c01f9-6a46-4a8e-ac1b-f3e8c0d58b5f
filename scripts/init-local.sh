#!/bin/bash

# Esus Audit AI - Local Development Setup Script
# This script sets up the local development environment

set -e

echo "🚀 Setting up Esus Audit AI for local development..."

# Check if Node.js is installed
if ! command -v node &> /dev/null; then
    echo "❌ Node.js is not installed. Please install Node.js 18+ first."
    exit 1
fi

# Check if PostgreSQL is installed
if ! command -v psql &> /dev/null; then
    echo "❌ PostgreSQL is not installed. Please install PostgreSQL first."
    exit 1
fi

# Check if Azure Functions Core Tools is installed
if ! command -v func &> /dev/null; then
    echo "❌ Azure Functions Core Tools is not installed."
    echo "   Please install it: npm install -g azure-functions-core-tools@4 --unsafe-perm true"
    exit 1
fi

echo "✅ Prerequisites check passed!"

# Install root dependencies
echo "📦 Installing root dependencies..."
npm install

# Install client dependencies
echo "📦 Installing client dependencies..."
cd client
npm install
cd ..

# Install server dependencies
echo "📦 Installing server dependencies..."
cd server
npm install
cd ..

# Create environment files if they don't exist
echo "⚙️  Setting up environment files..."

if [ ! -f "config/dev.env" ]; then
    echo "📝 Creating development environment file..."
    cp config/dev.env.example config/dev.env 2>/dev/null || echo "Please create config/dev.env manually"
fi

if [ ! -f "server/local.settings.json" ]; then
    echo "📝 Creating Azure Functions local settings..."
    cp server/local.settings.json.example server/local.settings.json 2>/dev/null || echo "Please create server/local.settings.json manually"
fi

# Setup database
echo "🗄️  Setting up database..."
echo "Please ensure PostgreSQL is running and create a database named 'esus_audit_ai_dev'"
echo "Then run the following commands:"
echo "  createdb esus_audit_ai_dev"
echo "  psql -d esus_audit_ai_dev -f database/schema.sql"
echo "  psql -d esus_audit_ai_dev -f database/seed.sql"

echo ""
echo "✅ Local setup completed!"
echo ""
echo "🔧 Next steps:"
echo "1. Update config/dev.env with your Azure service credentials"
echo "2. Update server/local.settings.json with your configuration"
echo "3. Set up PostgreSQL database and run migrations"
echo "4. Start the development servers:"
echo "   npm run start (starts both client and server)"
echo "   OR"
echo "   npm run start:client (frontend only)"
echo "   npm run start:server (backend only)"
echo ""
echo "📚 Documentation:"
echo "  - Frontend: http://localhost:3000"
echo "  - Backend: http://localhost:7071"
echo "  - API Docs: http://localhost:7071/api"
echo ""
echo "🔑 Initial admin account:"
echo "  Email: <EMAIL>"
echo "  Password: EsusAdmin2024! (change on first login)"