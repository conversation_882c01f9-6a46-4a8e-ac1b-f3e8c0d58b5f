# Esus Audit AI - Development Environment Configuration

# Database Configuration
DATABASE_URL=postgresql://username:password@localhost:5432/esus_audit_ai_dev
DB_HOST=localhost
DB_PORT=5432
DB_NAME=esus_audit_ai_dev
DB_USER=username
DB_PASSWORD=password

# Azure Configuration
AZURE_STORAGE_CONNECTION_STRING=DefaultEndpointsProtocol=https;AccountName=esusauditdev;AccountKey=your_storage_key_here;EndpointSuffix=core.windows.net
AZURE_STORAGE_CONTAINER_NAME=documents
AZURE_FORM_RECOGNIZER_ENDPOINT=https://your-form-recognizer.cognitiveservices.azure.com/
AZURE_FORM_RECOGNIZER_KEY=your_form_recognizer_key_here

# Azure OpenAI Configuration
AZURE_OPENAI_ENDPOINT=https://your-openai.openai.azure.com/
AZURE_OPENAI_API_KEY=your_openai_key_here
AZURE_OPENAI_DEPLOYMENT_NAME=gpt-4-turbo
AZURE_OPENAI_API_VERSION=2024-02-15-preview

# Azure Cognitive Search Configuration
AZURE_SEARCH_ENDPOINT=https://your-search-service.search.windows.net
AZURE_SEARCH_API_KEY=your_search_key_here
AZURE_SEARCH_INDEX_NAME=esus-audit-documents

# JWT Configuration
JWT_SECRET=your_super_secret_jwt_key_for_development_only
JWT_EXPIRES_IN=24h

# Application Configuration
NODE_ENV=development
PORT=3000
API_BASE_URL=http://localhost:7071/api
FRONTEND_URL=http://localhost:3000

# File Upload Configuration
MAX_FILE_SIZE=50MB
ALLOWED_FILE_TYPES=pdf,xlsx,xls,docx,doc,csv

# Email Configuration (for notifications)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASSWORD=your_app_password

# Logging
LOG_LEVEL=debug