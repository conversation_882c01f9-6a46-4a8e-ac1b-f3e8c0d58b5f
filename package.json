{"name": "esus-audit-ai", "version": "1.0.0", "description": "AI-powered audit automation platform for finance and audit firms", "main": "index.js", "scripts": {"start": "concurrently \"npm run start:client\" \"npm run start:server\"", "start:client": "cd client && npm start", "start:server": "cd server && func start", "build": "cd client && npm run build", "test": "cd client && npm test", "deploy": "./scripts/deploy.sh", "init": "./scripts/init-local.sh", "db:setup": "psql -f database/schema.sql && psql -f database/seed.sql"}, "keywords": ["audit", "ai", "finance", "azure", "react", "nodejs"], "author": "Esus Audit AI Team", "license": "MIT", "devDependencies": {"concurrently": "^8.2.2"}, "workspaces": ["client", "server"]}