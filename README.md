# Esus Audit AI

🤖 **AI-powered audit automation platform for finance and audit firms**

Esus Audit AI helps automate at least 50% of a human auditor's tasks by leveraging Azure AI services to analyze financial documents, detect compliance issues, and generate comprehensive audit reports.

## 🌟 Features

### Core Modules
- **📄 Document Upload & Parsing**: Upload PDFs, Excel files, invoices, and ledgers with automatic parsing using Azure Form Recognizer
- **🧠 AI Analysis**: Generate summaries, red flags, and highlights using Azure OpenAI (GPT-4 Turbo)
- **💬 Ask Esus (AI Advisor Chat)**: Interactive chat interface for querying audit findings and compliance issues
- **📊 Audit Report Generator**: Generate downloadable PDF reports with AI insights and recommendations

### Web App Features
- **🔐 Authentication**: JWT-based authentication with role-based access (admin, auditor, reviewer)
- **📈 Dashboard**: Overview of projects, documents, and analysis results
- **📁 Project Management**: Organize audits by client with document tracking
- **🔍 Smart Search**: Vector-based search through analyzed documents using Azure Cognitive Search

## 🏗️ Architecture

### Frontend (React + TailwindCSS)
- Modern React application with TypeScript support
- Responsive design with TailwindCSS
- Real-time updates with React Query
- File upload with drag-and-drop support
- Interactive chat interface

### Backend (Azure Functions - Node.js)
- Serverless architecture with Azure Functions
- RESTful API design
- Integration with Azure AI services
- PostgreSQL database for data persistence
- JWT authentication and authorization

### Azure Services
- **Azure OpenAI**: GPT-4 Turbo for document analysis and chat
- **Azure Form Recognizer**: Extract structured data from documents
- **Azure Blob Storage**: Secure file storage
- **Azure Cognitive Search**: Vector search for document retrieval
- **Azure Static Web Apps**: Frontend hosting
- **Azure Functions**: Serverless backend

## 🚀 Quick Start

### Prerequisites
- Node.js 18+
- PostgreSQL 12+
- Azure subscription
- Azure Functions Core Tools

### Local Development Setup

1. **Clone the repository**
   ```bash
   git clone https://github.com/your-username/esus-audit-ai.git
   cd esus-audit-ai
   ```

2. **Run the setup script**
   ```bash
   chmod +x scripts/init-local.sh
   ./scripts/init-local.sh
   ```

3. **Set up environment variables**
   ```bash
   # Copy and update the environment files
   cp config/dev.env.example config/dev.env
   cp server/local.settings.json.example server/local.settings.json
   ```

4. **Configure Azure services**
   Update the following in your environment files:
   - Azure OpenAI endpoint and API key
   - Azure Form Recognizer endpoint and key
   - Azure Blob Storage connection string
   - Azure Cognitive Search endpoint and key
   - PostgreSQL database connection

5. **Set up the database**
   ```bash
   createdb esus_audit_ai_dev
   psql -d esus_audit_ai_dev -f database/schema.sql
   psql -d esus_audit_ai_dev -f database/seed.sql
   ```

6. **Start the development servers**
   ```bash
   npm run start
   ```

   This starts both the frontend (http://localhost:3000) and backend (http://localhost:7071).

### Initial Setup
After running the database setup, you'll have an initial admin account:
- **Email**: <EMAIL>
- **Password**: EsusAdmin2024! (change on first login)

## 📁 Project Structure

```
esus-audit-ai/
├── client/                 # React frontend
│   ├── src/
│   │   ├── components/     # Reusable React components
│   │   ├── pages/          # Page components
│   │   ├── services/       # API services
│   │   ├── contexts/       # React contexts
│   │   └── utils/          # Utility functions
│   ├── public/             # Static assets
│   └── package.json
├── server/                 # Azure Functions backend
│   ├── auth/               # Authentication functions
│   ├── projects/           # Project management functions
│   ├── uploadDoc/          # Document upload function
│   ├── analyzeDoc/         # Document analysis function
│   ├── askEsus/            # AI chat function
│   ├── generateReport/     # Report generation function
│   ├── shared/             # Shared utilities
│   └── package.json
├── database/               # Database schema and seeds
│   ├── schema.sql          # Database schema
│   └── seed.sql            # Sample data
├── config/                 # Configuration files
│   ├── dev.env             # Development environment
│   └── prod.env            # Production environment
├── scripts/                # Deployment and setup scripts
│   ├── init-local.sh       # Local setup script
│   └── deploy.sh           # Azure deployment script
└── README.md
```

## 🔧 API Endpoints

### Authentication
- `POST /api/register` - User registration
- `POST /api/login` - User login
- `GET /api/verify` - Verify JWT token

### Projects
- `GET /api/projects` - Get user projects
- `POST /api/projects` - Create new project
- `GET /api/projects/{id}` - Get project details
- `PUT /api/projects/{id}` - Update project

### Documents
- `POST /api/uploadDoc` - Upload document
- `POST /api/analyzeDoc` - Analyze document
- `GET /api/documents/{id}/analysis` - Get analysis results

### AI Chat
- `POST /api/askEsus` - Ask AI question
- `GET /api/projects/{id}/chat` - Get chat history
- `GET /api/projects/{id}/suggested-questions` - Get suggested questions

### Reports
- `POST /api/generateReport` - Generate audit report
- `GET /api/projects/{id}/reports` - Get project reports

## 🔐 Environment Variables

### Required Azure Services
```bash
# Azure OpenAI
AZURE_OPENAI_ENDPOINT=https://your-openai.openai.azure.com/
AZURE_OPENAI_API_KEY=your_openai_key_here
AZURE_OPENAI_DEPLOYMENT_NAME=gpt-4-turbo

# Azure Form Recognizer
AZURE_FORM_RECOGNIZER_ENDPOINT=https://your-form-recognizer.cognitiveservices.azure.com/
AZURE_FORM_RECOGNIZER_KEY=your_form_recognizer_key_here

# Azure Blob Storage
AZURE_STORAGE_CONNECTION_STRING=DefaultEndpointsProtocol=https;AccountName=...

# Azure Cognitive Search
AZURE_SEARCH_ENDPOINT=https://your-search-service.search.windows.net
AZURE_SEARCH_API_KEY=your_search_key_here

# Database
DATABASE_URL=postgresql://username:password@localhost:5432/esus_audit_ai_dev

# JWT
JWT_SECRET=your_super_secret_jwt_key
```

## 🚀 Deployment

### Azure Deployment

1. **Install Azure CLI**
   ```bash
   # macOS
   brew install azure-cli

   # Windows
   winget install Microsoft.AzureCLI
   ```

2. **Login to Azure**
   ```bash
   az login
   ```

3. **Run deployment script**
   ```bash
   chmod +x scripts/deploy.sh
   ./scripts/deploy.sh
   ```

4. **Configure environment variables in Azure Portal**
   - Navigate to your Function App
   - Go to Configuration > Application settings
   - Add all required environment variables

### Manual Deployment Steps

1. **Create Azure Resources**
   - Resource Group
   - Storage Account
   - Function App
   - Static Web App
   - Azure OpenAI Service
   - Azure Form Recognizer
   - Azure Cognitive Search
   - PostgreSQL Database

2. **Deploy Backend**
   ```bash
   cd server
   func azure functionapp publish your-function-app-name
   ```

3. **Deploy Frontend**
   ```bash
   cd client
   npm run build
   # Deploy to Azure Static Web Apps
   ```

## 🧪 Testing

### Running Tests
```bash
# Frontend tests
cd client
npm test

# Backend tests
cd server
npm test
```

### Production Testing Workflow
1. Create a new audit project for a client
2. Upload financial documents (PDF statements, Excel spreadsheets)
3. Monitor AI analysis progress and review results
4. Use the AI chat to query specific audit findings
5. Generate comprehensive audit reports
6. Review compliance issues and red flags identified by AI

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add some amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🆘 Support

For support and questions:
- 📧 Email: <EMAIL>
- 📖 Documentation: [docs.esusaudit.ai](https://docs.esusaudit.ai)
- 🐛 Issues: [GitHub Issues](https://github.com/your-username/esus-audit-ai/issues)

## 🙏 Acknowledgments

- Azure AI Services for powerful document processing
- React and TailwindCSS communities
- Open source contributors

---

**Built with ❤️ for the audit and finance industry**