{"name": "esus-audit-ai-client", "version": "1.0.0", "private": true, "dependencies": {"@headlessui/react": "^1.7.17", "@heroicons/react": "^2.0.18", "axios": "^1.6.2", "react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.8.1", "react-dropzone": "^14.2.3", "react-query": "^3.39.3", "react-hot-toast": "^2.4.1", "jspdf": "^2.5.1", "html2canvas": "^1.4.1", "date-fns": "^2.30.0", "clsx": "^2.0.0"}, "devDependencies": {"@types/react": "^18.2.43", "@types/react-dom": "^18.2.17", "@vitejs/plugin-react": "^4.2.1", "autoprefixer": "^10.4.16", "postcss": "^8.4.32", "tailwindcss": "^3.3.6", "vite": "^5.0.8"}, "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "start": "vite"}, "eslintConfig": {"extends": ["react-app"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}