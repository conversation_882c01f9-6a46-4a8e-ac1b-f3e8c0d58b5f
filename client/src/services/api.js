import axios from 'axios';

// Create axios instance with base configuration
const api = axios.create({
  baseURL: import.meta.env.VITE_API_BASE_URL || '/api',
  timeout: 30000,
});

// Retry configuration
const MAX_RETRIES = 3;
const RETRY_DELAY = 1000; // 1 second

// Helper function for exponential backoff
const getRetryDelay = (retryCount) => {
  return RETRY_DELAY * Math.pow(2, retryCount);
};

// Helper function to check if error is retryable
const isRetryableError = (error) => {
  if (!error.response) return true; // Network errors are retryable
  const status = error.response.status;
  return status >= 500 || status === 408 || status === 429; // Server errors, timeout, rate limit
};

// Request interceptor to add auth token and retry logic
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }

    // Add retry metadata
    config.metadata = {
      retryCount: config.metadata?.retryCount || 0,
      startTime: config.metadata?.startTime || Date.now()
    };

    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor to handle errors and retries
api.interceptors.response.use(
  (response) => {
    // Log successful requests in development
    if (import.meta.env.DEV) {
      console.log(`✅ ${response.config.method?.toUpperCase()} ${response.config.url} - ${response.status}`);
    }
    return response.data;
  },
  async (error) => {
    const config = error.config;

    // Log errors in development
    if (import.meta.env.DEV) {
      console.error(`❌ ${config?.method?.toUpperCase()} ${config?.url} - ${error.response?.status || 'Network Error'}`, error);
    }

    // Handle authentication errors
    if (error.response?.status === 401) {
      localStorage.removeItem('token');
      window.location.href = '/login';
      return Promise.reject({
        error: 'Authentication required',
        code: 'AUTH_REQUIRED',
        status: 401
      });
    }

    // Handle retry logic
    if (config && isRetryableError(error) && config.metadata.retryCount < MAX_RETRIES) {
      config.metadata.retryCount += 1;
      const delay = getRetryDelay(config.metadata.retryCount - 1);

      console.log(`🔄 Retrying request (${config.metadata.retryCount}/${MAX_RETRIES}) after ${delay}ms`);

      await new Promise(resolve => setTimeout(resolve, delay));
      return api(config);
    }

    // Format error response
    const errorResponse = {
      error: error.response?.data?.error || error.message || 'An unexpected error occurred',
      code: error.response?.data?.code || error.code || 'UNKNOWN_ERROR',
      status: error.response?.status || 0,
      retries: config?.metadata?.retryCount || 0,
      duration: config?.metadata ? Date.now() - config.metadata.startTime : 0
    };

    return Promise.reject(errorResponse);
  }
);

// Auth service
export const authService = {
  login: async (credentials) => {
    return await api.post('/login', credentials);
  },

  register: async (userData) => {
    return await api.post('/register', userData);
  },

  verify: async () => {
    return await api.get('/verify');
  },

  updateProfile: async (profileData) => {
    return await api.put('/auth/profile', profileData);
  }
};

// Projects service
export const projectsService = {
  getProjects: async () => {
    return await api.get('/projects');
  },

  getProject: async (projectId) => {
    return await api.get(`/projects/${projectId}`);
  },

  createProject: async (projectData) => {
    return await api.post('/projects', projectData);
  },

  updateProject: async (projectId, updates) => {
    return await api.put(`/projects/${projectId}`, updates);
  }
};

// Documents service
export const documentsService = {
  uploadDocument: async (projectId, file, onProgress) => {
    try {
      // Validate inputs
      if (!projectId) {
        throw new Error('Project ID is required');
      }
      if (!file) {
        throw new Error('File is required');
      }

      // Validate file size (50MB limit)
      const maxSize = 50 * 1024 * 1024;
      if (file.size > maxSize) {
        throw new Error(`File size exceeds 50MB limit. Current size: ${(file.size / 1024 / 1024).toFixed(2)}MB`);
      }

      // Validate file type
      const allowedTypes = [
        'application/pdf',
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        'application/vnd.ms-excel',
        'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
        'application/msword',
        'text/csv'
      ];

      if (!allowedTypes.includes(file.type)) {
        throw new Error(`Unsupported file type: ${file.type}. Please upload PDF, Excel, Word, or CSV files.`);
      }

      const formData = new FormData();
      formData.append('file', file);
      formData.append('projectId', projectId);

      return await api.post('/uploadDoc', formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
        timeout: 120000, // 2 minutes for large files
        onUploadProgress: (progressEvent) => {
          if (onProgress) {
            const percentCompleted = Math.round(
              (progressEvent.loaded * 100) / progressEvent.total
            );
            onProgress(percentCompleted);
          }
        },
      });
    } catch (error) {
      // Enhanced error handling for uploads
      if (error.code === 'ECONNABORTED') {
        throw new Error('Upload timeout. Please try again with a smaller file or check your connection.');
      }
      if (error.status === 413) {
        throw new Error('File too large. Please reduce file size and try again.');
      }
      if (error.status === 415) {
        throw new Error('Unsupported file type. Please upload PDF, Excel, Word, or CSV files.');
      }
      throw error;
    }
  },

  analyzeDocument: async (documentId) => {
    try {
      if (!documentId) {
        throw new Error('Document ID is required');
      }

      return await api.post('/analyzeDoc', { documentId }, {
        timeout: 180000, // 3 minutes for analysis
      });
    } catch (error) {
      if (error.code === 'ECONNABORTED') {
        throw new Error('Analysis timeout. The document may be too complex. Please try again.');
      }
      if (error.status === 404) {
        throw new Error('Document not found. It may have been deleted.');
      }
      if (error.status === 422) {
        throw new Error('Document cannot be analyzed. Please check the file format and try again.');
      }
      throw error;
    }
  },

  getAnalysis: async (documentId) => {
    try {
      if (!documentId) {
        throw new Error('Document ID is required');
      }

      return await api.get(`/documents/${documentId}/analysis`);
    } catch (error) {
      if (error.status === 404) {
        throw new Error('Analysis not found. The document may not have been analyzed yet.');
      }
      throw error;
    }
  },

  // New method: Get document details
  getDocument: async (documentId) => {
    try {
      if (!documentId) {
        throw new Error('Document ID is required');
      }

      return await api.get(`/documents/${documentId}`);
    } catch (error) {
      if (error.status === 404) {
        throw new Error('Document not found.');
      }
      throw error;
    }
  },

  // New method: Delete document
  deleteDocument: async (documentId) => {
    try {
      if (!documentId) {
        throw new Error('Document ID is required');
      }

      return await api.delete(`/documents/${documentId}`);
    } catch (error) {
      if (error.status === 404) {
        throw new Error('Document not found.');
      }
      if (error.status === 403) {
        throw new Error('You do not have permission to delete this document.');
      }
      throw error;
    }
  }
};

// Chat service (Ask Esus)
export const chatService = {
  askEsus: async (projectId, question) => {
    return await api.post('/askEsus', { projectId, question });
  },

  getChatHistory: async (projectId, limit = 50) => {
    return await api.get(`/projects/${projectId}/chat?limit=${limit}`);
  },

  getSuggestedQuestions: async (projectId) => {
    return await api.get(`/projects/${projectId}/suggested-questions`);
  }
};

// Reports service
export const reportsService = {
  generateReport: async (projectId, reportName, includeCharts = false) => {
    try {
      if (!projectId) {
        throw new Error('Project ID is required');
      }
      if (!reportName || !reportName.trim()) {
        throw new Error('Report name is required');
      }

      return await api.post('/generateReport', {
        projectId,
        reportName: reportName.trim(),
        includeCharts
      }, {
        timeout: 300000, // 5 minutes for report generation
      });
    } catch (error) {
      if (error.code === 'ECONNABORTED') {
        throw new Error('Report generation timeout. Please try again or contact support for large projects.');
      }
      if (error.status === 404) {
        throw new Error('Project not found or no documents to analyze.');
      }
      if (error.status === 422) {
        throw new Error('Cannot generate report. Please ensure the project has analyzed documents.');
      }
      throw error;
    }
  },

  getReports: async (projectId) => {
    try {
      if (!projectId) {
        throw new Error('Project ID is required');
      }

      return await api.get(`/projects/${projectId}/reports`);
    } catch (error) {
      if (error.status === 404) {
        throw new Error('Project not found.');
      }
      throw error;
    }
  },

  // New method: Get specific report
  getReport: async (reportId) => {
    try {
      if (!reportId) {
        throw new Error('Report ID is required');
      }

      return await api.get(`/reports/${reportId}`);
    } catch (error) {
      if (error.status === 404) {
        throw new Error('Report not found.');
      }
      throw error;
    }
  },

  // New method: Download report PDF
  downloadReport: async (reportId) => {
    try {
      if (!reportId) {
        throw new Error('Report ID is required');
      }

      const response = await api.get(`/reports/${reportId}/download`, {
        responseType: 'blob',
        timeout: 60000, // 1 minute for download
      });

      // Create download link
      const url = window.URL.createObjectURL(new Blob([response]));
      const link = document.createElement('a');
      link.href = url;
      link.setAttribute('download', `report-${reportId}.pdf`);
      document.body.appendChild(link);
      link.click();
      link.remove();
      window.URL.revokeObjectURL(url);

      return { success: true };
    } catch (error) {
      if (error.status === 404) {
        throw new Error('Report not found or PDF not available.');
      }
      if (error.code === 'ECONNABORTED') {
        throw new Error('Download timeout. Please try again.');
      }
      throw error;
    }
  },

  // New method: Delete report
  deleteReport: async (reportId) => {
    try {
      if (!reportId) {
        throw new Error('Report ID is required');
      }

      return await api.delete(`/reports/${reportId}`);
    } catch (error) {
      if (error.status === 404) {
        throw new Error('Report not found.');
      }
      if (error.status === 403) {
        throw new Error('You do not have permission to delete this report.');
      }
      throw error;
    }
  }
};

export default api;