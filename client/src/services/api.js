import axios from 'axios';

// Create axios instance with base configuration
const api = axios.create({
  baseURL: import.meta.env.VITE_API_BASE_URL || '/api',
  timeout: 30000,
});

// Request interceptor to add auth token
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor to handle errors
api.interceptors.response.use(
  (response) => response.data,
  (error) => {
    if (error.response?.status === 401) {
      localStorage.removeItem('token');
      window.location.href = '/login';
    }
    return Promise.reject(error.response?.data || error.message);
  }
);

// Auth service
export const authService = {
  login: async (credentials) => {
    return await api.post('/login', credentials);
  },

  register: async (userData) => {
    return await api.post('/register', userData);
  },

  verify: async () => {
    return await api.get('/verify');
  },

  updateProfile: async (profileData) => {
    return await api.put('/auth/profile', profileData);
  }
};

// Projects service
export const projectsService = {
  getProjects: async () => {
    return await api.get('/projects');
  },

  getProject: async (projectId) => {
    return await api.get(`/projects/${projectId}`);
  },

  createProject: async (projectData) => {
    return await api.post('/projects', projectData);
  },

  updateProject: async (projectId, updates) => {
    return await api.put(`/projects/${projectId}`, updates);
  }
};

// Documents service
export const documentsService = {
  uploadDocument: async (projectId, file, onProgress) => {
    const formData = new FormData();
    formData.append('file', file);
    formData.append('projectId', projectId);

    return await api.post('/uploadDoc', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
      onUploadProgress: (progressEvent) => {
        if (onProgress) {
          const percentCompleted = Math.round(
            (progressEvent.loaded * 100) / progressEvent.total
          );
          onProgress(percentCompleted);
        }
      },
    });
  },

  analyzeDocument: async (documentId) => {
    return await api.post('/analyzeDoc', { documentId });
  },

  getAnalysis: async (documentId) => {
    return await api.get(`/documents/${documentId}/analysis`);
  }
};

// Chat service (Ask Esus)
export const chatService = {
  askEsus: async (projectId, question) => {
    return await api.post('/askEsus', { projectId, question });
  },

  getChatHistory: async (projectId, limit = 50) => {
    return await api.get(`/projects/${projectId}/chat?limit=${limit}`);
  },

  getSuggestedQuestions: async (projectId) => {
    return await api.get(`/projects/${projectId}/suggested-questions`);
  }
};

// Reports service
export const reportsService = {
  generateReport: async (projectId, reportName, includeCharts = false) => {
    return await api.post('/generateReport', {
      projectId,
      reportName,
      includeCharts
    });
  },

  getReports: async (projectId) => {
    return await api.get(`/projects/${projectId}/reports`);
  }
};

export default api;